import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import { resolve } from 'path'
import fs from 'fs'
import path from 'path'

// Function to check if node_modules/.vite exists and is older than package.json
const shouldForceOptimizeDeps = (): boolean => {
  try {
    const viteDir = path.join(__dirname, 'node_modules', '.vite')
    const packageJsonPath = path.join(__dirname, 'package.json')
    
    if (!fs.existsSync(viteDir)) {
      return true // Force if .vite doesn't exist
    }
    
    const viteStat = fs.statSync(viteDir)
    const packageStat = fs.statSync(packageJsonPath)
    
    // Force if package.json is newer than .vite directory
    return packageStat.mtime > viteStat.mtime
  } catch (error) {
    console.warn('Error checking optimization deps:', error)
    return false
  }
}

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/hooks': resolve(__dirname, 'src/hooks'),
      '@/services': resolve(__dirname, 'src/services'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/types': resolve(__dirname, 'src/types'),
      '@/styles': resolve(__dirname, 'src/styles'),
      '@/pages': resolve(__dirname, 'src/pages'),
      '@/context': resolve(__dirname, 'src/context')
    }
  },
  optimizeDeps: {
    force: shouldForceOptimizeDeps(),
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@mui/material',
      '@emotion/react',
      '@emotion/styled',
      'axios',
      'socket.io-client',
      'recharts'
    ]
  },
  build: {
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          mui: ['@mui/material', '@mui/icons-material', '@emotion/react', '@emotion/styled'],
          charts: ['recharts'],
          utils: ['axios', 'socket.io-client']
        }
      }
    }
  },
  server: {
    port: 50004,
    strictPort: false,
    host: '127.0.0.1',
    proxy: {
      '/api': {
        target: 'http://localhost:10002',
        changeOrigin: true,
        secure: false,
        timeout: 60000,
      },
      '/socket.io': {
        target: 'http://localhost:10002',
        changeOrigin: true,
        ws: true,
        secure: false,
      }
    }
  },
  preview: {
    port: 50004,
    strictPort: false,
    host: '127.0.0.1'
  }
})
