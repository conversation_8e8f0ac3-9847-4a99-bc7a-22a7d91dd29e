import React, { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ThemeProvider } from '@mui/material/styles'
import CssBaseline from '@mui/material/CssBaseline'
import { <PERSON>nackbarProvider } from 'notistack'

// Import contexts
import { AuthProvider } from './context/AuthContext'
import { SocketProvider } from './context/SocketContext'

// Import pages
import AuthPage from './pages/AuthPage'
import HomePage from './pages/HomePage'
import ProfilePage from './pages/ProfilePage'
import MessagesPage from './pages/MessagesPage'
import GalleryPage from './pages/GalleryPage'
import CreatePost from './pages/CreatePost'
import SettingsPage from './pages/SettingsPage'

// Import components
import ProtectedRoute from './components/ProtectedRoute'
import Layout from './components/Layout'
import { ErrorBoundary } from './components/ui'

// Import theme
import { lightTheme, darkTheme } from './styles/theme'
import { STORAGE_KEYS, THEME_MODES } from './utils/constants'

function App() {
  const [themeMode, setThemeMode] = useState(() => {
    const saved = localStorage.getItem(STORAGE_KEYS.THEME)
    return saved || THEME_MODES.LIGHT
  })

  const theme = themeMode === THEME_MODES.DARK ? darkTheme : lightTheme

  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.THEME, themeMode)
  }, [themeMode])

  return (
    <ErrorBoundary fallbackMessage="Something went wrong with the application. Please refresh the page.">
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <SnackbarProvider
          maxSnack={3}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          dense
        >
          <AuthProvider>
            <SocketProvider>
              <Router>
                <Routes>
                  {/* Public routes */}
                  <Route path="/auth" element={<AuthPage />} />

                  {/* Protected routes */}
                  <Route path="/" element={
                    <ProtectedRoute>
                      <Layout>
                        <HomePage />
                      </Layout>
                    </ProtectedRoute>
                  } />

                  <Route path="/profile/:id?" element={
                    <ProtectedRoute>
                      <Layout>
                        <ProfilePage />
                      </Layout>
                    </ProtectedRoute>
                  } />

                  <Route path="/messages" element={
                    <ProtectedRoute>
                      <Layout>
                        <MessagesPage />
                      </Layout>
                    </ProtectedRoute>
                  } />

                  <Route path="/gallery" element={
                    <ProtectedRoute>
                      <Layout>
                        <GalleryPage />
                      </Layout>
                    </ProtectedRoute>
                  } />

                  <Route path="/create" element={
                    <ProtectedRoute>
                      <Layout>
                        <CreatePost />
                      </Layout>
                    </ProtectedRoute>
                  } />

                  <Route path="/settings" element={
                    <ProtectedRoute>
                      <Layout>
                        <SettingsPage />
                      </Layout>
                    </ProtectedRoute>
                  } />
                </Routes>
              </Router>
            </SocketProvider>
          </AuthProvider>
        </SnackbarProvider>
      </ThemeProvider>
    </ErrorBoundary>
  )
}

export default App
